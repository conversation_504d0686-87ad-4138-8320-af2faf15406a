import Parent from "../models/parent.model.js";

// Create a new parent
export const createParent = async (req, res) => {
  try {
    const parentData = req.body;

    const newParent = new Parent(parentData);
    await newParent.save();

    res.status(201).json({
      success: true,
      message: "Parent created successfully",
      data: newParent,
    });
  } catch (error) {
    console.error("Create Parent Error:", error);

    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    res.status(500).json({
      success: false,
      message: "Internal server error. Please try again later.",
    });
  }
};

// Get all parents
export const getAllParents = async (req, res) => {
  try {
    const parents = await Parent.find().sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      count: parents.length,
      data: parents,
    });
  } catch (error) {
    console.error("Get All Parents Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Get parent by ID
export const getParentById = async (req, res) => {
  try {
    const parent = await Parent.findById(req.params.id);

    if (!parent) {
      return res.status(404).json({
        success: false,
        message: "Parent not found",
      });
    }

    res.status(200).json({
      success: true,
      data: parent,
    });
  } catch (error) {
    console.error("Get Parent By ID Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Update parent
export const updateParent = async (req, res) => {
  try {
    const parentData = req.body;

    const parent = await Parent.findByIdAndUpdate(req.params.id, parentData, {
      new: true,
      runValidators: true,
    });

    if (!parent) {
      return res.status(404).json({
        success: false,
        message: "Parent not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Parent updated successfully",
      data: parent,
    });
  } catch (error) {
    console.error("Update Parent Error:", error);

    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Delete parent
export const deleteParent = async (req, res) => {
  try {
    const parent = await Parent.findByIdAndDelete(req.params.id);

    if (!parent) {
      return res.status(404).json({
        success: false,
        message: "Parent not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Parent deleted successfully",
    });
  } catch (error) {
    console.error("Delete Parent Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Update parent status
export const updateParentStatus = async (req, res) => {
  try {
    const { status } = req.body;

    if (!status || !["active", "inactive"].includes(status)) {
      return res.status(400).json({
        success: false,
        message: "Please provide a valid status",
      });
    }

    const parent = await Parent.findByIdAndUpdate(
      req.params.id,
      { status },
      { new: true, runValidators: true }
    );

    if (!parent) {
      return res.status(404).json({
        success: false,
        message: "Parent not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Parent status updated successfully",
      data: parent,
    });
  } catch (error) {
    console.error("Update Parent Status Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};
