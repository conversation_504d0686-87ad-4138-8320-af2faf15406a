import { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useParent } from "@/context/parent-context";
import { PageHeader } from "@/components/dashboard/page-header";
import {
  Card,
  CardHeader,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { formatDate } from "@/utils/date-filters";
import {
  User,
  ArrowLeft,
  Edit,
  Trash2,
  MapPin,
  Briefcase,
  Calendar,
  FileText,
  Users,
} from "lucide-react";
import { Container } from "@/components/ui/container";

const ViewParent = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { fetchParentById, removeParent, updateStatus } = useParent();
  const [parent, setParent] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [openDelete, setOpenDelete] = useState(false);
  const [openStatusChange, setOpenStatusChange] = useState(false);

  useEffect(() => {
    const fetchParent = async () => {
      try {
        setIsLoading(true);
        const data = await fetchParentById(id);
        setParent(data);
      } catch (error) {
        console.error("Failed to fetch parent:", error);
        toast.error("Error", {
          description: error.message || "Failed to load parent details",
        });
        navigate("/dashboard/parents");
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchParent();
    } else {
      console.error("No parent ID provided");
      setIsLoading(false);
    }
  }, [id, navigate, fetchParentById]);

  const handleDelete = async () => {
    try {
      await removeParent(parent._id);
      toast.success("Parent deleted successfully", {
        description: `${parent.firstName} ${parent.lastName} has been removed.`,
      });
      navigate("/dashboard/parents");
    } catch (error) {
      console.error("Failed to delete parent:", error);
      toast.error("Delete Failed", {
        description: error.message || "Failed to delete parent",
      });
    }
  };

  const handleStatusChange = async () => {
    try {
      const newStatus = parent.status === "active" ? "inactive" : "active";
      await updateStatus(parent._id, { status: newStatus });

      setOpenStatusChange(false);

      setParent((prevParent) => ({
        ...prevParent,
        status: newStatus,
      }));

      toast.success("Status updated successfully", {
        description: `${parent.firstName} ${parent.lastName}'s status has been changed to ${newStatus}.`,
      });
    } catch (error) {
      console.error("Failed to update status:", error);
      toast.error("Update Failed", {
        description: error.message || "Failed to update parent status",
      });
    }
  };

  if (isLoading) {
    return <ParentDetailSkeleton />;
  }

  if (!parent) {
    return (
      <div className="min-h-screen bg-background">
        <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
          <PageHeader
            title="Parent Not Found"
            breadcrumbs={[
              { label: "Dashboard", href: "/dashboard" },
              { label: "Parents", href: "/dashboard/parents" },
              { label: "Not Found" },
            ]}
            actions={[
              {
                label: "Back to Parents",
                icon: ArrowLeft,
                href: "/dashboard/parents",
              },
            ]}
          />
          <Card className="py-0">
            <CardContent className="">
              <div className="text-center">
                <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-muted">
                  <User className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="mt-6 text-xl font-semibold text-foreground">
                  Parent not found
                </h3>
                <p className="mt-2 text-sm text-muted-foreground">
                  The parent you're looking for doesn't exist or has been
                  removed.
                </p>
                <Button
                  className="mt-6"
                  onClick={() => navigate("/dashboard/parents")}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Parents
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <Container className="py-8">
      <div className="space-y-6">
        <PageHeader
          title="Parent Details"
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Parents", href: "/dashboard/parents" },
            { label: `${parent.firstName} ${parent.lastName}` },
          ]}
          actions={[
            {
              label: "Edit",
              icon: Edit,
              href: `/dashboard/parents/${parent._id}/edit`,
            },
            {
              label: "Back to Parents",
              icon: ArrowLeft,
              href: "/dashboard/parents",
            },
          ]}
        />

        {/* Main Parent Card */}
        <Card className="py-0">
          <CardHeader className="border-b bg-card px-6 py-6 rounded-t-md">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="flex h-16 w-16 items-center justify-center rounded-full bg-muted border text-2xl font-bold">
                    {parent.firstName?.charAt(0) || ""}
                    {parent.lastName?.charAt(0) || ""}
                  </div>
                </div>
                <div className="min-w-0 flex-1">
                  <h1 className="text-2xl font-bold text-foreground mb-2">
                    {parent.title} {parent.firstName} {parent.lastName}
                  </h1>
                  <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                    <span className="font-medium">{parent.email}</span>
                    <span>•</span>
                    <span>{parent.phone}</span>
                    <span>•</span>
                    <span>ID: {parent._id}</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Badge
                  variant={
                    parent.status === "active" ? "success" : "destructive"
                  }
                >
                  {parent.status}
                </Badge>
              </div>
            </div>
          </CardHeader>

          <CardContent className="px-6 py-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Personal Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-foreground flex items-center">
                  <User className="mr-2 h-5 w-5 text-muted-foreground" />
                  Personal Information
                </h3>
                <div className="rounded-lg border bg-card p-4 space-y-3">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Gender
                      </label>
                      <p className="text-foreground font-medium">
                        {parent.gender || "Not specified"}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Date of Birth
                      </label>
                      <p className="text-foreground font-medium">
                        {parent.dateOfBirth
                          ? formatDate(parent.dateOfBirth)
                          : "Not specified"}
                      </p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Marital Status
                      </label>
                      <p className="text-foreground font-medium">
                        {parent.maritalStatus || "Not specified"}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Blood Group
                      </label>
                      <p className="text-foreground font-medium">
                        {parent.bloodGroup || "Not specified"}
                      </p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Nationality
                      </label>
                      <p className="text-foreground font-medium">
                        {parent.nationality || "Not specified"}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Religion
                      </label>
                      <p className="text-foreground font-medium">
                        {parent.religion || "Not specified"}
                      </p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Aadhar Number
                      </label>
                      <p className="text-foreground font-medium">
                        {parent.aadharNumber || "Not specified"}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        PAN Number
                      </label>
                      <p className="text-foreground font-medium">
                        {parent.panNumber || "Not specified"}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Address Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-foreground flex items-center">
                  <MapPin className="mr-2 h-5 w-5 text-muted-foreground" />
                  Address Information
                </h3>
                <div className="rounded-lg border bg-card p-4 space-y-3">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Address
                    </label>
                    <p className="text-foreground font-medium">
                      {parent.address || "Not specified"}
                    </p>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        City
                      </label>
                      <p className="text-foreground font-medium">
                        {parent.city || "Not specified"}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        State
                      </label>
                      <p className="text-foreground font-medium">
                        {parent.state || "Not specified"}
                      </p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Pincode
                      </label>
                      <p className="text-foreground font-medium">
                        {parent.pincode || "Not specified"}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Country
                      </label>
                      <p className="text-foreground font-medium">
                        {parent.country || "Not specified"}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Professional Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-foreground flex items-center">
                  <Briefcase className="mr-2 h-5 w-5 text-muted-foreground" />
                  Professional Information
                </h3>
                <div className="rounded-lg border bg-card p-4 space-y-3">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Occupation
                      </label>
                      <p className="text-foreground font-medium">
                        {parent.occupation || "Not specified"}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Company Name
                      </label>
                      <p className="text-foreground font-medium">
                        {parent.companyName || "Not specified"}
                      </p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Designation
                      </label>
                      <p className="text-foreground font-medium">
                        {parent.designation || "Not specified"}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Work Phone
                      </label>
                      <p className="text-foreground font-medium">
                        {parent.workPhone || "Not specified"}
                      </p>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Work Address
                    </label>
                    <p className="text-foreground font-medium">
                      {parent.workAddress || "Not specified"}
                    </p>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Qualification
                      </label>
                      <p className="text-foreground font-medium">
                        {parent.qualification || "Not specified"}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Annual Income
                      </label>
                      <p className="text-foreground font-medium">
                        {parent.annualIncome || "Not specified"}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Additional Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-foreground flex items-center">
                  <FileText className="mr-2 h-5 w-5 text-muted-foreground" />
                  Additional Information
                </h3>
                <div className="rounded-lg border bg-card p-4 space-y-3">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Medical Conditions
                    </label>
                    <p className="text-foreground font-medium">
                      {parent.medicalConditions || "None specified"}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Notes
                    </label>
                    <p className="text-foreground font-medium whitespace-pre-wrap">
                      {parent.notes || "No additional notes"}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Children Section */}
            <div className="mt-8">
              <h3 className="text-lg font-semibold text-foreground flex items-center mb-4">
                <Users className="mr-2 h-5 w-5 text-muted-foreground" />
                Children
              </h3>
              <div className="rounded-lg border bg-card p-4">
                {parent.children && parent.children.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {parent.children.map((child) => (
                      <div key={child._id} className="border rounded-md p-3">
                        <p className="font-medium">
                          {child.name || "Name not available"}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {child.class || "Class not available"}
                        </p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted-foreground">
                    No children associated with this parent.
                  </p>
                )}
              </div>
            </div>

            {/* System Information */}
            <div className="mt-8">
              <h3 className="text-lg font-semibold text-foreground flex items-center mb-4">
                <Calendar className="mr-2 h-5 w-5 text-muted-foreground" />
                System Information
              </h3>
              <div className="rounded-lg border bg-card p-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Created At
                    </label>
                    <p className="text-foreground font-medium">
                      {formatDate(parent.createdAt)}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Last Updated
                    </label>
                    <p className="text-foreground font-medium">
                      {formatDate(parent.updatedAt)}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>

          <CardFooter className="border-t px-6 py-4 bg-muted/50">
            <div className="flex justify-between w-full">
              <div>
                <Button
                  variant="outline"
                  onClick={() => setOpenStatusChange(true)}
                >
                  {parent.status === "active" ? "Deactivate" : "Activate"}{" "}
                  Parent
                </Button>
              </div>
              <div>
                <Button
                  variant="destructive"
                  onClick={() => setOpenDelete(true)}
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Parent
                </Button>
              </div>
            </div>
          </CardFooter>
        </Card>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={openDelete} onOpenChange={setOpenDelete}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Delete {parent.firstName} {parent.lastName}?
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this parent? This action cannot be
              undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Status Change Confirmation Dialog */}
      <AlertDialog open={openStatusChange} onOpenChange={setOpenStatusChange}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {parent.status === "active" ? "Deactivate" : "Activate"}{" "}
              {parent.firstName} {parent.lastName}?
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to{" "}
              {parent.status === "active" ? "deactivate" : "activate"} this
              parent?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleStatusChange}>
              {parent.status === "active" ? "Deactivate" : "Activate"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Container>
  );
};

// Skeleton loader for parent details
const ParentDetailSkeleton = () => {
  return (
    <Container className="py-8">
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="space-y-1">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-96" />
          </div>
          <div className="flex space-x-2">
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-24" />
          </div>
        </div>

        <Card className="py-0">
          <CardHeader className="border-b px-6 py-6">
            <div className="flex items-start space-x-4">
              <Skeleton className="h-16 w-16 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="h-6 w-64" />
                <Skeleton className="h-4 w-96" />
              </div>
            </div>
          </CardHeader>

          <CardContent className="px-6 py-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="space-y-4">
                  <Skeleton className="h-6 w-48" />
                  <div className="rounded-lg border p-4 space-y-3">
                    {[1, 2, 3].map((j) => (
                      <div key={j} className="space-y-1">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-5 w-full" />
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>

          <CardFooter className="border-t px-6 py-4">
            <div className="flex justify-between w-full">
              <Skeleton className="h-10 w-32" />
              <Skeleton className="h-10 w-32" />
            </div>
          </CardFooter>
        </Card>
      </div>
    </Container>
  );
};

export default ViewParent;
