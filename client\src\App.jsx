import { create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Router<PERSON>rovider } from "react-router-dom";
import { Toaster } from "sonner";

// Layout
import HomeLayout from "@/components/layout/home-layout";
import DashboardLayout from "@/components/layout/dashboard-layout";

// Pages
import Home from "@/pages/Home";
import ContactUs from "@/pages/ContactUs";
import NotFoundPage from "@/pages/NotFoundPage";

// Auth
import SignUp from "@/pages/auth/SignUp";
import SignIn from "@/pages/auth/SignIn";

// Auth Protection
import { ProtectedRoute } from "@/components/auth/protected-route";

// Role-based overview page
import RoleBasedOverview from "@/components/dashboard/role-based-overview";

// Admin
// Contact Directory
import ContactDirectory from "@/pages/dashboard/admin/contact/contact-directory";

// Schools
import CreateSchools from "@/pages/dashboard/admin/schools/create-schools";
import SchoolDirectory from "@/pages/dashboard/admin/schools/school-directory";

// School Admin
import CreateSchoolAdmin from "@/pages/dashboard/admin/school-admin/create-school-admin";
import SchoolAdminDirectory from "./pages/dashboard/admin/school-admin/school-admin-directory";

// Settings
import Settings from "@/pages/dashboard/Settings";

// Notifications
import CreateNotification from "@/pages/dashboard/notifications/create-notification";
import NotificationDirectory from "./pages/dashboard/notifications/notification-directory";
import ViewNotification from "./pages/dashboard/notifications/view-notification";

// School Admin
// Parents
import CreateParents from "./pages/dashboard/school-admin/parents/create-parents";
import ParentDirectory from "./pages/dashboard/school-admin/parents/parent-directory";
import ViewParent from "./pages/dashboard/school-admin/parents/view-parent";

const router = createBrowserRouter([
  {
    path: "/",
    element: <HomeLayout />,
    children: [
      {
        index: true,
        element: <Home />,
      },
      {
        path: "/contact-us",
        element: <ContactUs />,
      },
    ],
  },
  {
    path: "/sign-up",
    element: <SignUp />,
  },
  {
    path: "/sign-in",
    element: <SignIn />,
  },
  {
    path: "/dashboard",
    element: (
      <ProtectedRoute>
        <DashboardLayout />
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: <RoleBasedOverview />,
      },

      // ===== ADMIN ROUTES =====
      {
        path: "schools",
        element: (
          <ProtectedRoute allowedRoles={["admin"]}>
            <SchoolDirectory />
          </ProtectedRoute>
        ),
      },
      {
        path: "schools/create",
        element: (
          <ProtectedRoute allowedRoles={["admin"]}>
            <CreateSchools />
          </ProtectedRoute>
        ),
      },
      {
        path: "schools/:id",
        element: (
          <ProtectedRoute allowedRoles={["admin"]}>
            <h1>View School Details</h1>
          </ProtectedRoute>
        ),
      },
      {
        path: "schools/:id/edit",
        element: (
          <ProtectedRoute allowedRoles={["admin"]}>
            <CreateSchools />
          </ProtectedRoute>
        ),
      },
      {
        path: "school-admins",
        element: (
          <ProtectedRoute allowedRoles={["admin"]}>
            <SchoolAdminDirectory />
          </ProtectedRoute>
        ),
      },
      {
        path: "school-admins/create",
        element: (
          <ProtectedRoute allowedRoles={["admin"]}>
            <CreateSchoolAdmin />
          </ProtectedRoute>
        ),
      },
      {
        path: "school-admins/:id",
        element: (
          <ProtectedRoute allowedRoles={["admin"]}>
            <div>View School Admin</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "school-admins/:id/edit",
        element: (
          <ProtectedRoute allowedRoles={["admin"]}>
            <CreateSchoolAdmin />
          </ProtectedRoute>
        ),
      },
      {
        path: "contacts",
        element: (
          <ProtectedRoute allowedRoles={["admin"]}>
            <ContactDirectory />
          </ProtectedRoute>
        ),
      },

      // ===== SCHOOL ADMIN ROUTES =====
      {
        path: "teachers",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <div className="p-8">Teachers Directory</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "teachers/create",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <div className="p-8">Add New Teacher</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "teachers/attendance",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <div className="p-8">Teacher Attendance</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "teachers/performance",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <div className="p-8">Teacher Performance</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "students",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin", "teacher"]}>
            <div className="p-8">Students Directory</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "students/create",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <div className="p-8">Add New Student</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "students/attendance",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin", "teacher"]}>
            <div className="p-8">Student Attendance</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "students/performance",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin", "teacher"]}>
            <div className="p-8">Student Performance</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "parents",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <ParentDirectory />
          </ProtectedRoute>
        ),
      },
      {
        path: "parents/create",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <CreateParents />
          </ProtectedRoute>
        ),
      },
      {
        path: "parents/:id",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <ViewParent />
          </ProtectedRoute>
        ),
      },
      {
        path: "parents/:id/edit",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <CreateParents />
          </ProtectedRoute>
        ),
      },
      {
        path: "parents/communication",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin", "teacher"]}>
            <div className="p-8">Parent Communication</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "academics/classes",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <div className="p-8">Classes and Sections</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "academics/departments",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <div className="p-8">Departments</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "academics/subjects",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <div className="p-8">Subjects</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "academics/terms",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <div className="p-8">Academic Terms</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "schedule/calendar",
        element: (
          <ProtectedRoute
            allowedRoles={[
              "admin",
              "school-admin",
              "teacher",
              "student",
              "parent",
            ]}
          >
            <div className="p-8">School Calendar</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "schedule/events",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin"]}>
            <div className="p-8">School Events</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "schedule/timetable",
        element: (
          <ProtectedRoute
            allowedRoles={["admin", "school-admin", "teacher", "student"]}
          >
            <div className="p-8">School Timetable</div>
          </ProtectedRoute>
        ),
      },

      // ===== TEACHER ROUTES =====
      {
        path: "teacher/classes",
        element: (
          <ProtectedRoute allowedRoles={["teacher"]}>
            <div className="p-8">My Classes</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "teacher/attendance",
        element: (
          <ProtectedRoute allowedRoles={["teacher"]}>
            <div className="p-8">Take Attendance</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "teacher/assignments",
        element: (
          <ProtectedRoute allowedRoles={["teacher"]}>
            <div className="p-8">Assignments</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "teacher/timetable",
        element: (
          <ProtectedRoute allowedRoles={["teacher"]}>
            <div className="p-8">My Timetable</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "teacher/calendar",
        element: (
          <ProtectedRoute allowedRoles={["teacher"]}>
            <div className="p-8">My Calendar</div>
          </ProtectedRoute>
        ),
      },

      // ===== STUDENT ROUTES =====
      {
        path: "student/classes",
        element: (
          <ProtectedRoute allowedRoles={["student"]}>
            <div className="p-8">My Classes</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "student/assignments",
        element: (
          <ProtectedRoute allowedRoles={["student"]}>
            <div className="p-8">My Assignments</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "student/grades",
        element: (
          <ProtectedRoute allowedRoles={["student"]}>
            <div className="p-8">My Grades</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "student/timetable",
        element: (
          <ProtectedRoute allowedRoles={["student"]}>
            <div className="p-8">My Timetable</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "student/calendar",
        element: (
          <ProtectedRoute allowedRoles={["student"]}>
            <div className="p-8">My Calendar</div>
          </ProtectedRoute>
        ),
      },

      // ===== PARENT ROUTES =====
      {
        path: "parent/childrens",
        element: (
          <ProtectedRoute allowedRoles={["parent"]}>
            <div className="p-8">My Children</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "parent/childrens/performance",
        element: (
          <ProtectedRoute allowedRoles={["parent"]}>
            <div className="p-8">Children Performance</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "parent/childrens/attendance",
        element: (
          <ProtectedRoute allowedRoles={["parent"]}>
            <div className="p-8">Children Attendance</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "parent/children/grades",
        element: (
          <ProtectedRoute allowedRoles={["parent"]}>
            <div className="p-8">Children Grades</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "parent/messages",
        element: (
          <ProtectedRoute allowedRoles={["parent"]}>
            <div className="p-8">Messages</div>
          </ProtectedRoute>
        ),
      },

      // ===== COMMON ROUTES =====
      {
        path: "profile",
        element: (
          <ProtectedRoute>
            <div className="p-8">Profile</div>
          </ProtectedRoute>
        ),
      },
      {
        path: "notifications",
        element: (
          <ProtectedRoute>
            <NotificationDirectory />
          </ProtectedRoute>
        ),
      },
      {
        path: "notifications/:id",
        element: (
          <ProtectedRoute>
            <ViewNotification />
          </ProtectedRoute>
        ),
      },
      {
        path: "notifications/create",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin", "teacher"]}>
            <CreateNotification />
          </ProtectedRoute>
        ),
      },
      {
        path: "notifications/:id/edit",
        element: (
          <ProtectedRoute allowedRoles={["admin", "school-admin", "teacher"]}>
            <CreateNotification />
          </ProtectedRoute>
        ),
      },
      {
        path: "settings",
        element: (
          <ProtectedRoute>
            <Settings />
          </ProtectedRoute>
        ),
      },
    ],
  },
  {
    path: "*",
    element: <NotFoundPage />,
  },
]);

function App() {
  return (
    <>
      <Toaster />
      <RouterProvider router={router} />
    </>
  );
}

export default App;
